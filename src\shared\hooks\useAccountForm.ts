/**
 * @file Hook for managing account form state and logic with improved validation.
 */
import { zodResolver } from '@hookform/resolvers/zod';
import { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { ClipboardService } from '../../services/clipboardService';
import { accountSchema, type Account } from '../types/account';
import type { DiscoveredConfig } from '../types/protocol';

import { useEmailDiscovery } from './useEmailDiscovery';

// New form schema with separate host and port for better validation and UX
const formSchema = accountSchema.omit({ id: true, incoming: true, outgoing: true, connectionStatus: true, useProxy: true, proxy: true }).extend({
  incoming: z.object({
    protocol: z.enum(['imap', 'pop3']),
    host: z.string().min(1, 'Host cannot be empty'),
    port: z.number().min(1, 'Port cannot be empty'),
    useTls: z.boolean(),
  }),
  outgoing: z.object({
    protocol: z.literal('smtp'),
    host: z.string().min(1, 'Host cannot be empty'),
    port: z.number().min(1, 'Port cannot be empty'),
    useTls: z.boolean(),
  }).optional(),
});

export type AccountFormType = z.infer<typeof formSchema>;

// ... (rest of the interface remains the same)
interface UseAccountFormReturn {
    form: ReturnType<typeof useForm<AccountFormType>>;
    isPasswordVisible: boolean;
    setIsPasswordVisible: (visible: boolean) => void;
    error: string | null;
    setError: (error: string | null) => void;
    showProviderSuggestions: boolean;
    setShowProviderSuggestions: (show: boolean) => void;
    discovery: ReturnType<typeof useEmailDiscovery>;
    handleProviderSelect: (config: DiscoveredConfig) => void;
    handleEmailBlur: (e: React.FocusEvent<HTMLInputElement>) => Promise<void>;
    handleSubmit: (e?: React.BaseSyntheticEvent) => Promise<void>;
    parseCredentialsString: (text: string) => Promise<boolean>;
}


export const useAccountForm = (props: UseAccountFormProps): UseAccountFormReturn => {
  const { accountToEdit, initialData, onSave } = props;
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showProviderSuggestions, setShowProviderSuggestions] = useState(false);

  const discovery = useEmailDiscovery();
  const { discoverEmailSettings } = discovery;

  const form = useForm<AccountFormType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      displayName: '',
      email: '',
      password: '',
      incoming: {
        protocol: 'imap',
        host: 'imap.example.com',
        port: 993,
        useTls: true,
      },
    },
  });

  const { reset, setValue, trigger, handleSubmit: formHandleSubmit, getValues, watch } = form;

  useEffect(() => {
    if (accountToEdit) {
      reset(accountToEdit);
      return;
    }
    if (initialData) {
      setValue('email', initialData.email, { shouldValidate: true });
      setValue('password', initialData.password, { shouldValidate: true });
      setTimeout(() => {
        void discoverEmailSettings(initialData.email, false, setValue);
      }, 500);
      return;
    }
    reset({
        displayName: '',
        email: '',
        password: '',
        incoming: { protocol: 'imap', host: '', port: 993, useTls: true },
    });
  }, [accountToEdit, initialData, reset, setValue, discoverEmailSettings]);

  const handleProviderSelect = useCallback((config: DiscoveredConfig) => {
    discovery.applyDiscoveredConfig(config, setValue);
    setShowProviderSuggestions(false);
  }, [discovery, setValue]);

  const parseCredentialsString = useCallback(async (text: string): Promise<boolean> => {
    const result = ClipboardService.parseCredentialsString(text);
    if (result.success && result.credentials) {
      setValue('email', result.credentials.email, { shouldValidate: true });
      setValue('password', result.credentials.password, { shouldValidate: true });
      if (/^\S+@\S+\.\S+$/.test(result.credentials.email)) {
        await discovery.discoverEmailSettings(result.credentials.email, false, setValue);
      }
      return true;
    }
    return false;
  }, [setValue, discovery]);

  // Auto-discovery when email changes (with debounce)
  const [emailDebounceTimer, setEmailDebounceTimer] = useState<NodeJS.Timeout | null>(null);
  const watchedEmail = watch('email');

  useEffect(() => {
    if (!watchedEmail) return;

    // Clear previous timer
    if (emailDebounceTimer) {
      clearTimeout(emailDebounceTimer);
    }

    // Set new timer for auto-discovery
    const timer = setTimeout(async () => {
      if (/^\S+@\S+\.\S+$/.test(watchedEmail)) {
        const isValid = await trigger("email");
        if (isValid) {
          console.log('Auto-discovery triggered for:', watchedEmail);
          await discovery.discoverEmailSettings(watchedEmail, false, setValue);
        }
      }
    }, 1500); // 1.5 second delay

    setEmailDebounceTimer(timer);

    // Cleanup
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [watchedEmail]); // Only depend on watchedEmail

  const handleEmailBlur = useCallback(async (e: React.FocusEvent<HTMLInputElement>) => {
    const email = e.target.value.trim();
    if (await parseCredentialsString(email)) {
      return;
    }
    // Email blur now just handles credential parsing
    // Auto-discovery is handled by useEffect above
  }, [parseCredentialsString]);

  const handleSubmit = useCallback(async (e?: React.BaseSyntheticEvent) => {
    if (e) e.preventDefault();

    console.log('handleSubmit called');

    return formHandleSubmit(async (data: AccountFormType) => {
      console.log('Form data:', data);
      setError(null);

      // Run discovery if server settings are not configured or are using example values
      const needsDiscovery = !data.incoming.host ||
                           data.incoming.host.includes('example.com') ||
                           data.incoming.host === 'imap.example.com';

      if (needsDiscovery && /^\S+@\S+\.\S+$/.test(data.email)) {
        console.log('Running discovery before saving account...');
        try {
          await discovery.discoverEmailSettings(data.email, true, setValue);
          // Wait a bit for discovery to complete and form to update
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Get updated form data after discovery
          const updatedData = getValues();
          console.log('Updated data after discovery:', updatedData);

          // Use updated data if discovery was successful
          if (discovery.discoveryStatus === 'found') {
            Object.assign(data, updatedData);
          }
        } catch (discoveryError) {
          console.warn('Discovery failed, proceeding with manual settings:', discoveryError);
        }
      }

      const finalData: Omit<Account, 'id'> = {
        ...data,
        displayName: data.displayName || data.email.split('@')[0],
      };

      console.log('Final data to save:', finalData);

      try {
        await onSave?.(finalData);
        console.log('Account saved successfully');
      } catch (e: unknown) {
        console.error('Failed to save account:', e);
        const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
        setError(errorMessage);
      }
    })(e);
  }, [formHandleSubmit, onSave, discovery, setValue, getValues]);

  return {
    form,
    isPasswordVisible,
    setIsPasswordVisible,
    error,
    setError,
    showProviderSuggestions,
    setShowProviderSuggestions,
    discovery,
    handleProviderSelect,
    handleEmailBlur,
    handleSubmit,
    parseCredentialsString,
  };
};