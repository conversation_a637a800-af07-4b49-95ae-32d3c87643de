# Metacharts Project Map (High-Level Index)
# Auto-generated: 2025-07-28T00:53:14.381Z
# Purpose: Provides a high-level overview for AI navigation and developer onboarding.
# Stats: 102 files, ~15k lines, ~479k chars, ~~120k tokens

> Legend: An ellipsis (…) at the end of a description means it was truncated. Read the file for full details.

## `./`
- `components.json`: No description found.
- `package.json`: No description found.
- `tsconfig.json`: No description found.

## `src/`
- `app.tsx`: No description found.
- `main.ts`: No description found.
- `preload.ts`: Preload script for the renderer process.. Exposes a safe, type-strong API to the renderer for interacting with the main process.
- `renderer.tsx`: No description found.

## `src/components/`
- `EmailListPanel.tsx`: Panel that contains the list of mailboxes.. /
- `EmailListSkeleton.tsx`: Component that shows a loading skeleton for the email list.. /
- `EmailListView.tsx`: Renders the list of emails with infinite scroll, search filtering, and modern design.. /
- `EmailViewer.tsx`: No description found.
- `EmailViewPanel.tsx`: This panel acts as a container for either the email list view or the email content view.. /
- `ErrorBoundary.tsx`: Error boundary component for catching and displaying React errors gracefully. /
- `FormField.tsx`: Accessible form field components with validation using ShadCN UI. /
- `ImportDialog.tsx`: Simple import dialog for account files with drag-and-drop. /
- `Layout.tsx`: The main layout component using react-resizable-panels.. It sets up the three-panel view for the application.
- `LoadingSpinner.tsx`: Loading spinner component with accessibility features using ShadCN UI. /
- `LogPanel.tsx`: A persistent sidebar panel for displaying application logs.. /
- `VirtualizedList.tsx`: Virtualized list component for performance optimization with large datasets. /

## `src/components/AccountManager/`
- `AccountContextMenu.tsx`: Context menu component for account actions. /
- `AccountForm.tsx`: Refactored account form component for a better user experience.. /
- `AccountItem.tsx`: Individual account item component for the account manager. /
- `AccountManagerPanel.tsx`: Panel for managing email accounts with a modern dark design. /
- `index.ts`: Entry point for AccountManager components. /
- `ProviderSuggestions.tsx`: Provider suggestions component for account form. /
- `ServerConfigSection.tsx`: Server configuration section for account form. /

## `src/components/AccountManager/components/`
- `ServerSettingsSection.tsx`: Server settings section component for account form. /

## `src/components/SettingsPanel/`
- `MainSettings.tsx`: Main application settings component. /
- `ProxyAddForm.tsx`: Proxy add form component. /
- `ProxyAddFormComponents.tsx`: Proxy add form sub-components. /
- `ProxyAdvancedSettings.tsx`: Proxy advanced settings component. /
- `ProxyAdvancedSettingsComponents.tsx`: Proxy advanced settings sub-components. /
- `ProxyImportPanel.tsx`: Proxy import panel component. /
- `ProxyList.tsx`: Proxy list component. /
- `ProxySettings.tsx`: Modern proxy settings component with intuitive interface for all use cases.. /
- `ProxyStatusHeader.tsx`: Proxy status header component. /
- `SettingsView.tsx`: Component for displaying settings directly in the main content area. /

## `src/ipc/`
- `account.ts`: No description found.
- `config.ts`: IPC handlers for user configuration management. /
- `imapFlow.ts`: No description found.
- `index.ts`: Entry point for registering all IPC handlers.. It imports handlers from different files and registers them with the main process.
- `proxy.ts`: No description found.

## `src/services/`
- `accountImportService.ts`: High-performance account import service with data normalization. Handles parsing and normalization of IMAP account data from various formats
- `autoDiscoveryService.ts`: Service for auto-discovering email server settings, now with parallel execution and caching.. /
- `clipboardService.ts`: Service for clipboard operations and credential parsing. /
- `emailSanitizationService.ts`: Email content sanitization service. Simple service for handling email content display
- `imapFlowConnectionManager.ts`: Manages active IMAP connections for user accounts using ImapFlow.. /
- `imapFlowService.ts`: Service for handling IMAP connections and operations using ImapFlow.. /
- `instantImportService.ts`: Instant import service with background DNS discovery. Imports accounts immediately and discovers server settings in background
- `storeService.ts`: Service for managing storage using simple text files.. This stores everything in the application's root directory for portability.

## `src/services/discovery/`
- `connectionTesting.ts`: Connection testing utilities for email discovery. /
- `dnsDiscovery.ts`: DNS-based email discovery using modern techniques.. /
- `exchangeDiscovery.ts`: Microsoft Exchange Autodiscover implementation using modern techniques.. /
- `providerDiscovery.ts`: Provider-based email discovery. /
- `types.ts`: Types for email discovery services. /

## `src/services/utils/`
- `emailProcessing.ts`: Email processing utilities. /
- `imapErrorHandling.ts`: IMAP error handling utilities. /

## `src/shared/hooks/`
- `useAccountForm.ts`: Hook for managing account form state and logic with improved validation.. /
- `useAccountManager.ts`: Hook for managing account operations and state. /
- `useAppInitialization.ts`: Hook for managing application initialization. /
- `useEmailDiscovery.ts`: Hook for email server auto-discovery functionality. /
- `useEmailList.ts`: Hook for managing email list operations and state. /
- `useEmailViewer.ts`: Hook for managing email viewer functionality. /
- `useKeyboardNavigation.ts`: Custom hook for keyboard navigation support. /
- `useMailboxManager.ts`: Hook for managing mailbox functionality. /
- `useProxyManager.ts`: Hook for managing proxy operations and state. /
- `useProxyStatus.ts`: Hook for managing proxy status functionality. /

## `src/shared/store/`
- `accountStore.ts`: Zustand store for managing account-related state in the renderer process.. This store holds information about user accounts, selections,…
- `imapProviders.ts`: Contains a list of common email provider IMAP settings.. This is used for autodetecting server settings when a user adds an account.
- `logStore.ts`: Zustand store for managing a persistent log of application events.. /
- `mainSettingsStore.ts`: Main application settings store. /
- `proxyListStore.ts`: Zustand store for managing proxy list state.. /
- `proxyStore.ts`: Zustand store for managing global proxy state.. /
- `uiStore.ts`: No description found.

## `src/shared/types/`
- `account.ts`: Account types and schemas for email account management. /
- `electron.ts`: Electron type definitions for IPC communication. /
- `email.ts`: Email types for headers and content. /
- `protocol.ts`: Protocol types for email server configuration and discovery. /

## `src/shared/ui/`
- `avatar.tsx`: Simple avatar component without Radix UI dependency. /
- `badge.tsx`: No description found.
- `button.tsx`: No description found.
- `card.tsx`: No description found.
- `checkbox.tsx`: No description found.
- `dialog.tsx`: No description found.
- `index.ts`: Entry point for UI components. /
- `input.tsx`: No description found.
- `label.tsx`: No description found.
- `progress.tsx`: No description found.
- `select.tsx`: No description found.
- `skeleton.tsx`: Skeleton loading component using ShadCN UI. /
- `sonner.tsx`: No description found.
- `switch.tsx`: No description found.
- `tabs.tsx`: No description found.
- `theme-provider.tsx`: No description found.
- `Toast.tsx`: Toast notification system using Sonner with ShadCN UI integration. /
- `toggle-group.tsx`: No description found.
- `toggle.tsx`: No description found.
- `tooltip.tsx`: No description found.
- `top-bar-account-section.tsx`: Account section component for the top bar. /
- `top-bar.tsx`: Unified top bar component for consistent application header. /

## `src/shared/utils/`
- `security.ts`: Security utilities for sanitizing and validating user input. /
- `utils.ts`: Utility functions for ShadCN UI components and general use. /